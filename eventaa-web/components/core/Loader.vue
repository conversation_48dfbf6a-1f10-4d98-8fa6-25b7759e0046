<template>
  <span
    class="loader"
    :style="{
      borderColor: color + ' transparent',
      borderWidth: borderWidth + 'px',
      width: typeof width === 'number' ? width + 'px' : width,
      height: typeof height === 'number' ? height + 'px' : height
    }"
  ></span>
</template>

<script setup>
const props = defineProps({
  color: {
    type: String,
    default: 'oklch(57.7% 0.245 27.325)'
  },
  width: {
    type: [Number, String],
    default: 70
  },
  height: {
    type: [Number, String],
    default: 70
  }
});

// Calculate border width based on size (roughly 1/8 of the width for good proportions)
const borderWidth = computed(() => {
  const size = typeof props.width === 'number' ? props.width : parseInt(props.width) || 70;
  return Math.max(2, Math.round(size / 8)); // Minimum 2px, scales with size
});
</script>

<style>
.loader {
  border-style: solid;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
