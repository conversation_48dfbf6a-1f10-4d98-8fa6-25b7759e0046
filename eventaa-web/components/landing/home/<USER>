<template>
  <div class="relative overflow-hidden">
    <div class="moving-background"></div>
    <div class="flex flex-col sm:py-20 py-8 bg-cover bg-center text-white relative z-10">
      <div class="sm:p-8 p-4">
        <h3 class="text-5xl sm:text-6xl font-semibold bg-gradient-to-r from-red-50 to-white bg-clip-text text-transparent leading-tight sm:leading-normal">
          The best event management app in Malawi
        </h3>
        <p class="font-medium text-gray-100 text-base mt-2 sm:mt-0">
          Find "Festivals, Parties, Conferences, Arts & Theater, Sports"
          events and more...
        </p>
        <div class="w-full flex flex-col sm:flex-row items-center mt-4 sm:mt-3 bg-white">
          <div class="w-full flex items-center sm:flex-row flex-col">
            <div v-if="computedCategories.length > 1" class="border-b sm:border-b-0 sm:border-r w-full sm:w-auto">
              <CoreImageDropdown :items="computedCategories" v-model="selectedCategory" />
            </div>
            <input type="search" class="w-full outline-none focus:outline-none bg-white px-3 py-3 sm:px-2 sm:py-2 text-gray-500"
              placeholder="Search for events" v-model="search" @keydown.enter="onSearch" />
          </div>
          <div class="flex items-center justify-center w-full sm:w-auto space-x-2 sm:space-x-3 px-3 py-2 sm:px-2 sm:py-2 mt-0 border-t sm:border-t-0 sm:border-l border-gray-100">
            <div class="relative">
              <button class="bg-gray-50 border border-gray-100 rounded-full px-2 py-2"
                @mouseenter="locationTooltip = true" @mouseleave="locationTooltip = false" @click="allowCurrentLocation">
                <Icon icon="tabler:current-location" class="w-5 h-5"
                  :class="useCurrentLocation ? 'text-sky-500' : 'text-gray-400'" />
              </button>
              <div v-if="locationTooltip"
                class="absolute text-sm font-medium left-1/2 transform -translate-x-1/2 -translate-y-full -top-1/2 bg-black bg-opacity-25 p-2 text-gray-100 shadow-md pointer-events-none">
                Click here to search by your current location
                <div
                  class="absolute left-1/2 transform -translate-x-1/2 translate-y-full w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-black border-opacity-25">
                </div>
              </div>
            </div>

            <button v-if="!isLocationFetching" :disabled="isLocationFetching" @click="onSearch"
              class="flex-1 sm:w-auto bg-red-600 hover:bg-red-700 transition duration-300 ease-in-out text-white px-4 py-2 flex items-center justify-center space-x-2 font-medium">
              <Icon icon="gg:search" class="w-4 h-4 sm:w-5 sm:h-5" />
              <span class="text-base">Search</span>
            </button>
            <div v-else class="flex-1 sm:w-auto text-sky-500 font-thin flex items-center justify-center space-x-2">
              <CoreLoader width="20" height="20" color="#0ea5e9" class="sm:mr-2"/>
              <span class="text-sm">Fetching...</span>
            </div>
          </div>
        </div>
        <div class="flex flex-wrap gap-2 py-3 rounded-lg">
          <button v-for="(item, index) in searchItems" :key="index" @click="onSearchTags(item)"
            class="flex flex-row items-center text-sm sm:text-base bg-black bg-opacity-25 hover:bg-opacity-50 transition duration-150 rounded-full px-3 py-1">
            <Icon icon="icon-park-twotone:search" class="mr-1 w-3 h-3 sm:w-4 sm:h-4" />
            {{ item }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Category } from '@/types';

const emits = defineEmits(['onQuery']);
const locationTooltip = ref<boolean>(false);
const useCurrentLocation = ref<boolean>(false);
const { $categories, $toast }: any = useNuxtApp();
const defaultCategory = { id: 0, name: 'select', icon: 'other.png' };
const loadingCategory = { id: -1, name: 'Loading categories...', icon: 'other.png' };

const computedCategories = computed(() => {
  if (!$categories || !$categories.value) {
    return [defaultCategory, loadingCategory];
  }

  const categoryList = Array.isArray($categories.value) ? $categories.value : [];
  return [defaultCategory, ...categoryList];
});
const searchItems: string[] = ["Trending", "Nearby Me", "Recommended"];
const selectedCategory = ref<Category>(defaultCategory);
const search = ref<string>("");
const isLocationFetching = ref<boolean>(false);
const location = ref<any>({});
const { getCurrentLocation } = useGeolocation();

const allowCurrentLocation = async (): Promise<void> => {
  useCurrentLocation.value = !useCurrentLocation.value;
  if (useCurrentLocation.value) {
    isLocationFetching.value = true;

    try {
      const response = await Promise.race([
        getCurrentLocation(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Location request timeout')), 10000)
        )
      ]) as any;

      if (response && response.latitude && response.longitude) {
        location.value = response;
        $toast?.success("Nearby events will be shown in search results!");
      } else {
        throw new Error('Invalid location response');
      }
    } catch (error: any) {
      console.log('Location fetch failed:', error.message);
      useCurrentLocation.value = false;
      $toast?.info("Continue without location - you can enable it later.");
    } finally {
      isLocationFetching.value = false;
    }
  } else {
    location.value = {};
  }
}

const onSearch = async (): Promise<void> => {
  if (useCurrentLocation.value && location.value.latitude && location.value.longitude) {
    emits('onQuery', {
      search: search.value,
      category: selectedCategory.value.id,
      location: location.value,
    });
  } else {
    performDefaultSearch();

    if (isLocationFetching.value) {
      $toast?.info("Searching all events. Location will be applied when available.");
    }
  }
}

const performDefaultSearch = () => {
  emits('onQuery', {
    search: search.value,
    category: selectedCategory.value.id,
    location: null
  });
}

const onSearchTags = async (tag: string): Promise<void> => {
  if (tag.toLowerCase() === "nearby me") {
    // For nearby me, we need to get location first
    if (!location.value.latitude || !location.value.longitude) {
      await allowCurrentLocation();
      if (!location.value.latitude || !location.value.longitude) {
        $toast?.error("Location permission is required for nearby events");
        return;
      }
    }
    // Navigate with location data
    emits('onQuery', {
      search: '',
      category: 0,
      location: location.value,
    });
  } else {
    const tagParam = tag.toLowerCase();
    navigateTo(`/events?tag=${tagParam}`);
  }
}
</script>

<style lang="scss" scoped>
.moving-background {
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  background-image: url("../../../assets/images/hero.png");
  background-size: cover;
  background-position: center;
  animation: moveBackground 30s linear infinite;
}

@keyframes moveBackground {
  0% {
    transform: translate(0, 0) scale(1.1);
  }

  50% {
    transform: translate(-20px, -20px) scale(1.2);
  }

  100% {
    transform: translate(0, 0) scale(1.1);
  }
}

.body {
  justify-content: baseline;
}
</style>
