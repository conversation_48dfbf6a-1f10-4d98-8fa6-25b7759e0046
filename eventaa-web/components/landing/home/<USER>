<template>
  <div
    class="w-full py-16 bg-gray-50 dark:bg-zinc-800 transition-colors duration-200"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-5xl font-bold text-gray-900 dark:text-white mb-4">
          Latest from our Community
        </h2>
        <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          See what people are saying about events in Malawi on social media
        </p>
      </div>

      <div v-if="loading" class="flex justify-center items-center py-12">
        <CoreLoader :width="60" :height="60" color="red" />
      </div>

      <div v-else-if="error" class="text-center py-12">
        <div
          class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 max-w-md mx-auto"
        >
          <Icon
            icon="heroicons:exclamation-triangle"
            class="w-8 h-8 text-red-600 dark:text-red-400 mx-auto mb-3"
          />
          <p class="text-red-800 dark:text-red-200">{{ error }}</p>
        </div>
      </div>

      <div
        v-else-if="tweets.length > 0"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        <BlogTwitterCard
          v-for="tweet in tweets"
          :key="tweet.id"
          :tweet="tweet"
          class="transform hover:scale-105 transition-transform duration-200"
        />
      </div>

      <div v-else class="text-center py-12">
        <Icon
          icon="heroicons:chat-bubble-left-ellipsis"
          class="w-16 h-16 text-gray-400 dark:text-gray-600 mx-auto mb-4"
        />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No tweets yet
        </h3>
        <p class="text-gray-600 dark:text-gray-400">
          Check back later for the latest social media updates!
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Tweet {
  id: number;
  tweet_id: string;
  content: string;
  author_id: string;
  tweet_created_at: string;
  hashtag: string;
  metadata: any;
  created_at: string;
  updated_at: string;
}

interface TwitterApiResponse {
  data: Tweet[];
  cached_at: string;
  count: number;
}

const tweets = ref<Tweet[]>([]);
const loading = ref<boolean>(false);
const error = ref<string | null>(null);

const fetchTweets = async (): Promise<void> => {
  loading.value = true;
  error.value = null;

  try {
    const httpClient = useHttpClient();
    const response = await httpClient.get<TwitterApiResponse>(
      ENDPOINTS.BLOG.TWITTER
    );

    if (response && response.data) {
      tweets.value = response.data.slice(0, 6);
    } else {
      tweets.value = [];
    }
  } catch (err: any) {
    console.error("Error fetching tweets:", err);
    error.value = "Unable to load tweets at the moment";
    tweets.value = [];
  } finally {
    loading.value = false;
  }
};

const refreshTweets = async (): Promise<void> => {
  await fetchTweets();
};

onMounted(() => {
  fetchTweets();
});
</script>
