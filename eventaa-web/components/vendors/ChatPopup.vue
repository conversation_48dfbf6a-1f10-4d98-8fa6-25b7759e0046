<template>
  <div>
    <div v-if="!isOpen" class="fixed bottom-6 right-6 z-[999]">
      <button @click="openChat"
        class="bg-red-600 hover:bg-red-700 text-white rounded-full p-3 shadow-lg transition-all duration-300 flex items-center justify-center">
        <Icon icon="heroicons:chat-bubble-left-right" class="w-8 h-8" />
      </button>
    </div>

    <div v-if="isOpen"
      class="fixed bottom-6 right-6 z-50 w-80 sm:w-96 bg-white dark:bg-zinc-800 shadow-xl flex flex-col overflow-hidden border border-gray-200 dark:border-zinc-700">
      <div class="p-4 bg-red-600 text-white flex justify-between items-center">
        <div class="flex items-center">
          <div class="relative">
            <img v-if="vendor?.logo" :src="`${runtimeConfig.public.baseUrl}storage/${vendor.logo}`" :alt="vendor?.name"
              class="w-8 h-8 rounded-full mr-2 object-cover" />
            <div v-else class="w-8 h-8 rounded-full mr-2 bg-red-700 flex items-center justify-center">
              <span class="text-sm font-bold">{{ getInitials(vendor?.name || '') }}</span>
            </div>
            <span class="absolute bottom-0 right-1 w-3 h-3 rounded-full"
              :class="vendorOnline ? 'bg-green-500' : 'bg-gray-400'"
              :title="vendorOnline ? 'Online' : 'Offline'"></span>
          </div>
          <div>
            <h3 class="font-medium">{{ vendor?.name || 'Chat' }}</h3>
            <span class="text-xs opacity-80">{{ vendorOnline ? 'Online' : 'Offline' }}</span>
          </div>
        </div>
        <button @click="closeChat" class="text-white hover:text-gray-200">
          <Icon icon="heroicons:x-mark" class="w-5 h-5" />
        </button>
      </div>

      <div ref="messagesContainer"
        class="flex-1 p-3 overflow-y-auto max-h-80 min-h-40 bg-cover bg-center"
        style="background-image: url('/hero.png');">
        <div v-if="loading" class="flex justify-center items-center h-full">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        </div>

        <div v-else-if="!conversation && !loading"
          class="flex flex-col items-center justify-center h-full text-center p-4 bg-white bg-opacity-80 dark:bg-zinc-900 dark:bg-opacity-80 rounded-lg">
          <Icon icon="heroicons:chat-bubble-left-right" class="w-12 h-12 text-gray-300 dark:text-gray-600 mb-2" />
          <p class="text-gray-500 dark:text-gray-400">Start a conversation with {{ vendor?.name }}</p>
        </div>

        <template v-else>
          <div v-for="(message, index) in messages" :key="index" class="mb-4">
            <div class="flex items-start"
              :class="message.user_id === authStore.user?.id ? 'justify-end' : 'justify-start'">
              <template v-if="message.user_id !== authStore.user?.id">
                <div class="flex-shrink-0 mr-2 mt-1">
                  <img v-if="vendor?.logo" :src="`${runtimeConfig.public.baseUrl}storage/${vendor.logo}`"
                    :alt="vendor?.name" class="w-8 h-8 rounded-full object-cover" />
                  <div v-else class="w-8 h-8 rounded-full bg-red-700 flex items-center justify-center">
                    <span class="text-sm font-bold text-white">{{ getInitials(vendor?.name || '') }}</span>
                  </div>
                </div>
              </template>

              <div :class="[
                message.user_id === authStore.user?.id
                  ? 'bg-red-600 text-white rounded-tl-lg rounded-tr-lg rounded-bl-lg'
                  : 'bg-white dark:bg-zinc-800 text-gray-800 dark:text-gray-200 rounded-tl-lg rounded-tr-lg rounded-br-lg',
                'px-4 py-2 max-w-[80%] shadow-md relative'
              ]">
                <p class="text-sm break-words">{{ message.content }}</p>
                <div class="flex items-center justify-end mt-1 space-x-1">
                  <span class="text-xs opacity-70">
                    {{ formatTime(message.created_at) }}
                  </span>

                  <!-- Message status indicators (only for user's messages) -->
                  <template v-if="message.user_id === authStore.user?.id">
                    <Icon v-if="message.is_read" icon="heroicons:check-circle" class="w-3 h-3 text-blue-400" title="Read" />
                    <Icon v-else icon="heroicons:check" class="w-3 h-3 opacity-70" title="Sent" />
                  </template>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>

      <div class="p-3 border-t border-gray-200 dark:border-zinc-700">
        <form v-if="authStore.userIsAuthenticated" @submit.prevent="sendMessage" class="flex">
          <input v-model="newMessage" type="text" placeholder="Type a message..."
            class="flex-1 border border-gray-300 dark:border-zinc-600 px-3 py-2 focus:outline-none dark:text-white"
            :disabled="loading || sendingMessage || !authStore.userIsAuthenticated" />
          <button type="submit"
            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 transition-colors duration-200 flex items-center justify-center min-w-[48px]"
            :disabled="loading || sendingMessage || !newMessage.trim() || !authStore.userIsAuthenticated">
            <div v-if="sendingMessage" class="animate-spin rounded-full h-4 w-4 border-2 border-white"></div>
            <Icon v-else icon="heroicons:paper-airplane" class="w-5 h-5" />
          </button>
        </form>
        <div v-else class="mt-2 text-center text-sm text-red-600">
          Please <LandingAuthenticationLogin/> to send messages
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useAuthStore } from '@/store/auth';
import { ENDPOINTS } from '@/utils/api';

dayjs.extend(relativeTime);

interface Message {
  id: number;
  conversation_id: number;
  user_id: number;
  content: string;
  is_read: boolean;
  read_at: string | null;
  created_at: string;
  updated_at: string;
}

const props = defineProps<{
  vendorId: string;
  vendorName?: string;
  vendorLogo?: string;
}>();

const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const sendingMessage = ref<boolean>(false);
const conversation = ref<any>(null);
const messages = ref<Message[]>([]);
const newMessage = ref<string>('');
const messagesContainer = ref<HTMLElement | null>(null);
const vendor = ref<any>(null);
const vendorOnline = ref<boolean>(false);

const authStore = useAuthStore();
const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const nuxtApp = useNuxtApp();
const { $toast }: any = nuxtApp;
const $echo = nuxtApp.$echo as any;

const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

const formatTime = (timestamp: string): string => {
  const date = dayjs(timestamp);
  const now = dayjs();

  if (now.diff(date, 'day') < 1) {
    return date.format('h:mm A');
  } else if (now.diff(date, 'week') < 1) {
    return date.format('ddd h:mm A');
  } else {
    return date.format('MMM D, YYYY');
  }
};

const scrollToBottom = async (): Promise<void> => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const fetchVendor = async (): Promise<void> => {
  try {
    const response: any = await httpClient.get(`${ENDPOINTS.VENDORS.READ}/${props.vendorId}`);
    if (response) {
      vendor.value = response;
      // After getting vendor, check their online status
      checkVendorOnlineStatus();
    }
  } catch (error) {
    console.error('Error fetching vendor data:', error);
  }
};

const checkVendorOnlineStatus = async (): Promise<void> => {
  if (!vendor.value?.user_id) return;

  try {
    const response: any = await httpClient.get(`${ENDPOINTS.MESSAGES.BASE}/status/${vendor.value.user_id}`);
    if (response) {
      vendorOnline.value = response.is_online;
    }
  } catch (error) {
    console.error('Error checking vendor online status:', error);
    vendorOnline.value = false;
  }
};

const getOrCreateConversation = async (): Promise<void> => {
  if (!authStore.isAuthenticated) return;

  loading.value = true;
  try {
    const response: any = await httpClient.post(`${ENDPOINTS.MESSAGES.GET_OR_CREATE_CONVERSATION}`, {
      vendor_id: vendor.value.id
    });

    if (response && response.conversation) {
      conversation.value = response.conversation;
      fetchMessages();
    }
  } catch (error: any) {
    console.error('Error creating conversation:', error);
    $toast.error(error.response?.data?.message || 'Failed to start conversation');
    loading.value = false;
  }
};

const fetchMessages = async (): Promise<void> => {
  if (!conversation.value?.id || !authStore.isAuthenticated) {
    loading.value = false;
    return;
  }

  try {
    const response: any = await httpClient.get(`${ENDPOINTS.MESSAGES.GET_MESSAGES}/${conversation.value.id}`);
    if (response) {
      messages.value = response.data ?
        [...response.data].sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()) :
        [];
      await scrollToBottom();
    }
  } catch (error) {
    console.error('Error fetching messages:', error);
    $toast.error('Failed to load messages');
  } finally {
    loading.value = false;
  }
};

const sendMessage = async (): Promise<void> => {
  if (!newMessage.value.trim() || !conversation.value?.id || !authStore.isAuthenticated) return;

  const messageContent = newMessage.value;
  newMessage.value = '';
  sendingMessage.value = true;

  try {
    const response: any = await httpClient.post(`${ENDPOINTS.MESSAGES.SEND}`, {
      conversation_id: conversation.value.id,
      content: messageContent
    });

    if (response && response.data) {
      messages.value.push(response.data);
      await scrollToBottom();
    }
  } catch (error) {
    console.error('Error sending message:', error);
    $toast.error('Failed to send message');
    newMessage.value = messageContent; // Restore message if failed
  } finally {
    sendingMessage.value = false;
  }
};

const openChat = async (): Promise<void> => {
  isOpen.value = true;
  if (authStore.isAuthenticated) {
    await getOrCreateConversation();
  }
};

const closeChat = (): void => {
  isOpen.value = false;
};

watch(() => conversation.value, (newConversation) => {
  if (newConversation?.id && $echo && authStore.isAuthenticated) {
    try {
      $echo.private(`conversation.${newConversation.id}`)
        .listen('.message.sent', (event: any) => {
          console.log('Received message event:', event);
          if (event.message && event.user.id !== authStore.user?.id) {
            messages.value.push(event.message);
            scrollToBottom();
          }
        });
    } catch (error) {
      console.error('Failed to subscribe to conversation channel:', error);
    }
  }
});

onMounted(() => {
  fetchVendor();
});

defineExpose({
  openChat,
  closeChat
});
</script>
