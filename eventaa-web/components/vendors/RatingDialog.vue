<template>
  <div>
    <button @click="openModal"
      class="inline-flex items-center px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600 focus:outline-none">
      <Icon icon="heroicons:star" class="w-5 h-5 mr-2" />
      Rate Vendor
    </button>
  </div>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-[9999]">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 shadow-xl transition-all rounded-lg">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white p-4 border-b dark:border-zinc-700">
                Rate Your Experience
              </DialogTitle>
              <div v-if="loading" class="p-6 flex justify-center">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-amber-500"></div>
              </div>
              <div v-else class="p-6">
                <FormKit type="form" id="ratingForm" @submit="submitRating" :actions="false" class="space-y-4">
                  <div class="flex flex-col items-center mb-4">
                    <h4 class="text-lg font-medium mb-2 dark:text-white">How was your experience?</h4>
                    <CoreStarRating v-model="rating" :show-rating="true" />
                  </div>

                  <FormKit type="textarea" label="Your Review (Optional)" name="comment" v-model="comment"
                    placeholder="Share your experience with this vendor" rows="4" />

                  <div class="flex justify-end space-x-3 mt-6">
                    <button type="button"
                      class="px-4 py-2 bg-white dark:bg-zinc-700 text-gray-700 dark:text-white border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-600 rounded"
                      @click="closeModal">
                      Cancel
                    </button>
                    <FormKit type="submit" label="Submit Rating"
                      input-class="px-4 py-2 bg-amber-500 text-white hover:bg-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 rounded">
                      <template #prefix v-if="actionLoading">
                        <div class="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                      </template>
                    </FormKit>
                  </div>
                </FormKit>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue';
import { ref, computed } from 'vue';
import { ENDPOINTS } from '@/utils/api';

const props = defineProps<{
  vendorId: number;
}>();

const emit = defineEmits(['rating-submitted']);

const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const actionLoading = ref<boolean>(false);
const rating = ref<number>(0);
const comment = ref<string>('');
const { $toast }: any = useNuxtApp();
const httpClient = useHttpClient();

const openModal = async (): Promise<void> => {
  loading.value = true;
  try {
    const response: {rating: number; comment: string} = await httpClient.get(`${ENDPOINTS.VENDORS.RATINGS.USER}/${props.vendorId}`);
    if (response) {
      rating.value = response.rating;
      comment.value = response.comment || '';
    }
  } catch (error) {
    rating.value = 0;
    comment.value = '';
  } finally {
    loading.value = false;
    isOpen.value = true;
  }
};

const closeModal = (): void => {
  isOpen.value = false;
  resetForm();
};

const resetForm = (): void => {
  rating.value = 0;
  comment.value = '';
};

const submitRating = async (): Promise<void> => {
  if (rating.value === 0) {
    $toast.error('Please select a rating');
    return;
  }

  actionLoading.value = true;

  try {
    const ratingData: {vendor_id: number; rating: number; comment: string} = {
      vendor_id: props.vendorId,
      rating: rating.value,
      comment: comment.value
    };

    const response: {id: number; rating: number; comment: string} = await httpClient.post(`${ENDPOINTS.VENDORS.RATINGS.CREATE}`, ratingData);

    if (response) {
      $toast.success('Rating submitted successfully');
      emit('rating-submitted', response);
      closeModal();
    } else {
      $toast.error('Failed to submit rating');
    }
  } catch (error: any) {
    $toast.error(error.message || 'An error occurred');
  } finally {
    actionLoading.value = false;
  }
};

defineExpose({
  openModal
});
</script>
