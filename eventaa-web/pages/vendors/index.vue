<template>
    <div>
        <vendor-hero />
        <div class="p-3 md:p-5">
            <!-- Mobile Filter Button -->
            <div class="md:hidden mb-4">
                <button
                    @click="showMobileFilters = true"
                    class="w-full flex items-center justify-center gap-2 bg-red-600 text-white py-3 px-4 font-medium"
                >
                    <Icon icon="heroicons:funnel" class="w-5 h-5" />
                    Filters & Sort
                    <span v-if="activeFiltersCount > 0" class="bg-white text-red-600 text-xs px-2 py-1 rounded-full font-semibold">
                        {{ activeFiltersCount }}
                    </span>
                </button>
            </div>

            <div class="w-full grid grid-cols-1 md:grid-cols-6 gap-4">
                <!-- Desktop Filters -->
                <div class="hidden md:block col-span-1 md:col-span-2">
                    <div class="w-full p-4 bg-white dark:bg-zinc-800 shadow">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-bold text-zinc-800 dark:text-white">Filters</h2>
                            <button @click="resetFilters" class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                Reset All
                            </button>
                        </div>

                        <div v-if="isLoadingFilters" class="py-8">
                            <div class="flex justify-center">
                                <CoreLoader :width="20" :height="20" color="red" />
                            </div>
                            <p class="text-center mt-2 text-zinc-600 dark:text-zinc-400">Loading filters...</p>
                        </div>

                        <div v-else class="space-y-4">
                            <div class="border-b pb-4 dark:border-zinc-700">
                                <div @click="toggleSection('services')"
                                    class="text-zinc-700 dark:text-zinc-300 flex justify-between items-center cursor-pointer mb-2">
                                    <h3 class="text-lg font-semibold">Services</h3>
                                    <svg class="w-3 h-3" :class="{ 'transform rotate-180': !expanded.services }"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                        <path fill="currentColor"
                                            d="M3 19h18a1.002 1.002 0 0 0 .823-1.569l-9-13c-.373-.539-1.271-.539-1.645 0l-9 13A.999.999 0 0 0 3 19" />
                                    </svg>
                                </div>
                                <div v-if="expanded.services" class="space-y-2 mt-2">
                                    <div v-if="filterOptions.services.length === 0" class="text-zinc-500 dark:text-zinc-400 text-sm italic">
                                        No services available
                                    </div>
                                    <div v-for="service in filterOptions.services" :key="service.id" class="flex items-center">
                                        <input
                                            type="checkbox"
                                            :id="`service-${service.id}`"
                                            :value="service.id"
                                            v-model="selectedServices"
                                            @change="applyFilters"
                                            class="h-5 w-5 rounded border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                        >
                                        <label :for="`service-${service.id}`" class="ml-3 text-base text-zinc-600 dark:text-zinc-400">
                                            {{ service.name }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="border-b pb-4 dark:border-zinc-700">
                                <div @click="toggleSection('priceRange')"
                                    class="text-zinc-700 dark:text-zinc-300 flex justify-between items-center cursor-pointer mb-2">
                                    <h3 class="text-lg font-semibold">Price Range</h3>
                                    <svg class="w-3 h-3" :class="{ 'transform rotate-180': !expanded.priceRange }"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                        <path fill="currentColor"
                                            d="M3 19h18a1.002 1.002 0 0 0 .823-1.569l-9-13c-.373-.539-1.271-.539-1.645 0l-9 13A.999.999 0 0 0 3 19" />
                                    </svg>
                                </div>
                                <div v-if="expanded.priceRange" class="space-y-4 mt-2">
                                    <div class="flex justify-between items-center">
                                        <span class="text-zinc-600 dark:text-zinc-400">{{ formatCurrency(priceRange[0]) }}</span>
                                        <span class="text-zinc-600 dark:text-zinc-400">{{ formatCurrency(priceRange[1]) }}</span>
                                    </div>
                                    <div class="px-2">
                                        <input
                                            type="range"
                                            v-model="priceRange[0]"
                                            :min="filterOptions.price_range.min"
                                            :max="filterOptions.price_range.max"
                                            @change="applyFilters"
                                            class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                                        >
                                        <input
                                            type="range"
                                            v-model="priceRange[1]"
                                            :min="filterOptions.price_range.min"
                                            :max="filterOptions.price_range.max"
                                            @change="applyFilters"
                                            class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 mt-2"
                                        >
                                    </div>
                                </div>
                            </div>

                            <div class="border-b pb-4 dark:border-zinc-700">
                                <div @click="toggleSection('location')"
                                    class="text-zinc-700 dark:text-zinc-300 flex justify-between items-center cursor-pointer mb-2">
                                    <h3 class="text-lg font-semibold">Location</h3>
                                    <svg class="w-3 h-3" :class="{ 'transform rotate-180': !expanded.location }"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                        <path fill="currentColor"
                                            d="M3 19h18a1.002 1.002 0 0 0 .823-1.569l-9-13c-.373-.539-1.271-.539-1.645 0l-9 13A.999.999 0 0 0 3 19" />
                                    </svg>
                                </div>
                                <div v-if="expanded.location" class="space-y-2 mt-2">
                                    <div v-if="filterOptions.locations.length === 0" class="text-zinc-500 dark:text-zinc-400 text-sm italic">
                                        No locations available
                                    </div>
                                    <div v-for="(location, index) in filterOptions.locations" :key="index" class="flex items-center">
                                        <input
                                            type="checkbox"
                                            :id="`location-${index}`"
                                            :value="location"
                                            v-model="selectedLocations"
                                            @change="applyFilters"
                                            class="h-5 w-5 rounded border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                        >
                                        <label :for="`location-${index}`" class="ml-3 text-base text-zinc-600 dark:text-zinc-400">
                                            {{ location }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="border-b pb-4 dark:border-zinc-700">
                                <div @click="toggleSection('rating')"
                                    class="text-zinc-700 dark:text-zinc-300 flex justify-between items-center cursor-pointer mb-2">
                                    <h3 class="text-lg font-semibold">Minimum Rating</h3>
                                    <svg class="w-3 h-3" :class="{ 'transform rotate-180': !expanded.rating }"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                        <path fill="currentColor"
                                            d="M3 19h18a1.002 1.002 0 0 0 .823-1.569l-9-13c-.373-.539-1.271-.539-1.645 0l-9 13A.999.999 0 0 0 3 19" />
                                    </svg>
                                </div>
                                <div v-if="expanded.rating" class="space-y-2 mt-2">
                                    <div class="flex items-center">
                                        <input
                                            type="radio"
                                            id="rating-any"
                                            name="rating"
                                            :value="0"
                                            v-model="minRating"
                                            @change="applyFilters"
                                            class="h-5 w-5 border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                        >
                                        <label for="rating-any" class="ml-3 text-base text-zinc-600 dark:text-zinc-400">Any rating</label>
                                    </div>
                                    <div v-for="rating in [3, 4]" :key="rating" class="flex items-center">
                                        <input
                                            type="radio"
                                            :id="`rating-${rating}`"
                                            name="rating"
                                            :value="rating"
                                            v-model="minRating"
                                            @change="applyFilters"
                                            class="h-5 w-5 border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                        >
                                        <label :for="`rating-${rating}`" class="ml-3 text-base text-zinc-600 dark:text-zinc-400 flex items-center">
                                            {{ rating }}+
                                            <div class="flex ml-1">
                                                <svg v-for="i in rating" :key="i" class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                </svg>
                                                <svg v-for="i in (5-rating)" :key="i+rating" class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                </svg>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="pb-2">
                                <div @click="toggleSection('availability')"
                                    class="text-zinc-700 dark:text-zinc-300 flex justify-between items-center cursor-pointer mb-2">
                                    <h3 class="text-lg font-semibold">Availability</h3>
                                    <svg class="w-3 h-3" :class="{ 'transform rotate-180': !expanded.availability }"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                        <path fill="currentColor"
                                            d="M3 19h18a1.002 1.002 0 0 0 .823-1.569l-9-13c-.373-.539-1.271-.539-1.645 0l-9 13A.999.999 0 0 0 3 19" />
                                    </svg>
                                </div>
                                <div v-if="expanded.availability" class="space-y-2 mt-2">
                                    <div class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="available-only"
                                            v-model="availableOnly"
                                            @change="applyFilters"
                                            class="h-5 w-5 rounded border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                        >
                                        <label for="available-only" class="ml-3 text-base text-zinc-600 dark:text-zinc-400">
                                            Available vendors only
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-1 md:col-span-4">
                    <!-- Desktop Search and Sort -->
                    <div class="hidden md:block bg-white dark:bg-zinc-800 shadow-sm p-4 mb-6">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                            <div class="flex-1 mb-4 md:mb-0 md:mr-4">
                                <CoreSearch v-model="searchQuery" @update:modelValue="applyFilters" />
                            </div>
                            <div class="flex items-center">
                                <label for="sort" class="text-base font-semibold text-zinc-900 dark:text-zinc-300 mr-2">Sort by:</label>
                                <div class="relative w-56">
                                    <Listbox v-model="sortOption" @update:modelValue="applyFilters">
                                        <div class="relative">
                                            <ListboxButton
                                                class="relative w-full py-1.5 border border-gray-300 dark:border-zinc-600 pl-3 pr-10 text-left bg-white dark:bg-zinc-700 text-zinc-900 dark:text-white focus:outline-none focus:ring-0 focus:ring-red-500 focus:border-transparent">
                                                <span class="block truncate">{{ getSortLabel(sortOption) }}</span>
                                                <span
                                                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                                    <ChevronDownIcon class="h-5 w-5 text-gray-400 dark:text-zinc-400" aria-hidden="true" />
                                                </span>
                                            </ListboxButton>

                                            <transition leave-active-class="transition duration-100 ease-in"
                                                leave-from-class="opacity-100" leave-to-class="opacity-0">
                                                <ListboxOptions
                                                    class="absolute mt-1 max-h-60 w-full overflow-auto bg-white dark:bg-zinc-700 py-1 shadow-lg border dark:border-zinc-600 focus:outline-none z-10">
                                                    <ListboxOption v-for="option in sortOptions" :key="option.value"
                                                        :value="option.value" v-slot="{ active, selected }">
                                                        <li :class="[
                                                            active ? 'bg-red-100 text-red-900 dark:bg-red-900 dark:text-red-100' : 'text-gray-900 dark:text-white',
                                                            'relative cursor-default select-none py-2 pl-3 pr-9'
                                                        ]">
                                                            <span
                                                                :class="[selected ? 'font-medium' : 'font-normal', 'block truncate']">
                                                                {{ option.label }}
                                                            </span>

                                                            <span v-if="selected"
                                                                class="absolute inset-y-0 right-0 flex items-center pr-3 text-red-600 dark:text-red-400">
                                                                <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                                            </span>
                                                        </li>
                                                    </ListboxOption>
                                                </ListboxOptions>
                                            </transition>
                                        </div>
                                    </Listbox>
                                </div>
                            </div>
                            <div class="relative flex ml-5">
                                <div class="relative">
                                    <button @click="changeView('grid')" :class="[
                                        toggleGrid
                                            ? 'bg-red-600 text-white'
                                            : 'bg-gray-200 dark:bg-zinc-700 text-gray-500 dark:text-zinc-300',
                                        'p-1.5 shiny',
                                    ]">
                                        <Icon icon="ep:grid" class="w-6 h-6" />
                                    </button>
                                    <span v-if="toggleGrid"
                                        class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
                                </div>

                                <div class="relative">
                                    <button @click="changeView('list')" :class="[
                                        toggleGrid
                                            ? 'bg-gray-200 dark:bg-zinc-700 text-gray-500 dark:text-zinc-300'
                                            : 'bg-red-600 text-white',
                                        'p-1.5 shiny',
                                    ]">
                                        <Icon icon="ph:list-bold" class="w-6 h-6" />
                                    </button>
                                    <span v-if="!toggleGrid"
                                        class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Search Bar -->
                    <div class="md:hidden bg-white dark:bg-zinc-800 shadow-sm p-3 mb-4">
                        <CoreSearch v-model="searchQuery" @update:modelValue="applyFilters" />
                    </div>

                    <div v-if="isLoading" class="mt-4">
                        <div v-if="toggleGrid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-3 md:gap-4">
                            <vendor-card-skeleton v-for="i in 4" :key="`skeleton-${i}`" />
                        </div>
                        <div v-else class="flex flex-col gap-3">
                            <vendor-listview-skeleton v-for="i in 4" :key="`skeleton-${i}`" />
                        </div>
                    </div>

                    <div v-else>
                        <div v-if="toggleGrid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-3 md:gap-4 mt-4">
                            <vendor-card v-for="vendor in vendors" :key="vendor.id" :vendor="vendor" />
                            <div v-if="vendors.length === 0" class="col-span-full py-12 md:py-16 text-center px-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 md:h-16 md:w-16 mx-auto text-gray-400 dark:text-gray-600"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="mt-4 text-lg md:text-xl font-medium text-gray-600 dark:text-gray-400">No vendors match your search criteria
                                </p>
                                <p class="mt-2 text-sm md:text-base text-gray-500 dark:text-gray-500">Try adjusting your filters or search terms</p>
                            </div>
                        </div>

                        <div v-else class="flex flex-col gap-3 mt-4">
                            <vendor-listview v-for="vendor in vendors" :key="vendor.id" :vendor="vendor" />
                            <div v-if="vendors.length === 0" class="py-12 md:py-16 text-center px-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 md:h-16 md:w-16 mx-auto text-gray-400 dark:text-gray-600"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="mt-4 text-lg md:text-xl font-medium text-gray-600 dark:text-gray-400">No vendors match your search criteria
                                </p>
                                <p class="mt-2 text-sm md:text-base text-gray-500 dark:text-gray-500">Try adjusting your filters or search terms</p>
                            </div>
                        </div>
                    </div>

                    <div v-if="!isLoading && pagination && vendors.length > 0" class="mt-6 md:mt-8 flex flex-col items-center px-3">
                        <span class="text-sm md:text-base text-gray-700 dark:text-gray-300 text-center">
                            Showing
                            <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.from || 0 }}</span>
                            to
                            <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.to || 0 }}</span>
                            of
                            <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.total }}</span>
                            Entries
                        </span>

                        <div class="inline-flex mt-3 md:mt-2 gap-1 flex-wrap justify-center">
                            <button @click="changePage(pagination.current_page - 1)"
                                :disabled="!pagination.prev_page_url" :class="[
                                    'flex items-center justify-center px-3 md:px-4 h-9 md:h-10 text-sm md:text-base font-medium text-white',
                                    pagination.prev_page_url
                                        ? 'bg-red-600 hover:bg-red-700 cursor-pointer'
                                        : 'bg-zinc-300 dark:bg-zinc-700 cursor-not-allowed text-zinc-500 dark:text-zinc-400'
                                ]">
                                Prev
                            </button>

                            <div class="flex gap-1">
                                <button v-for="page in getPageNumbers()" :key="page" @click="changePage(page)" :class="[
                                    'flex items-center justify-center px-3 md:px-4 h-9 md:h-10 text-sm md:text-base font-medium',
                                    page === pagination.current_page
                                        ? 'bg-red-600 text-white'
                                        : 'bg-zinc-100 text-gray-700 hover:bg-gray-200 dark:bg-zinc-800 dark:text-gray-200 dark:hover:bg-zinc-700'
                                ]">
                                    {{ page }}
                                </button>
                            </div>

                            <button @click="changePage(pagination.current_page + 1)"
                                :disabled="!pagination.next_page_url" :class="[
                                    'flex items-center justify-center px-3 md:px-4 h-9 md:h-10 text-sm md:text-base font-medium text-white',
                                    pagination.next_page_url
                                        ? 'bg-red-600 hover:bg-red-700 cursor-pointer'
                                        : 'bg-gray-300 dark:bg-zinc-700 cursor-not-allowed text-zinc-500 dark:text-zinc-400'
                                ]">
                                Next
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Mobile Filter Modal -->
        <div v-if="showMobileFilters" class="fixed inset-0 z-[9999] md:hidden">
            <div class="fixed inset-0 bg-black bg-opacity-50" @click="showMobileFilters = false"></div>
            <div class="fixed inset-y-0 right-0 w-full max-w-sm bg-white dark:bg-zinc-800 shadow-xl transform transition-transform duration-300 ease-in-out">
                <div class="flex flex-col h-full">
                    <!-- Header -->
                    <div class="flex items-center justify-between p-4 border-b dark:border-zinc-700">
                        <h2 class="text-lg font-semibold text-zinc-800 dark:text-white">Filters & Sort</h2>
                        <button @click="showMobileFilters = false" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <Icon icon="heroicons:x-mark" class="w-6 h-6" />
                        </button>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 overflow-y-auto p-4">
                        <!-- Search -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Search</label>
                            <CoreSearch v-model="searchQuery" @update:modelValue="applyFilters" />
                        </div>

                        <!-- Sort -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Sort by</label>
                            <select v-model="sortOption" @change="applyFilters"
                                class="w-full p-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-900 dark:text-white focus:ring-red-500 focus:border-red-500">
                                <option v-for="option in sortOptions" :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>

                        <!-- View Toggle -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">View</label>
                            <div class="flex gap-2">
                                <button @click="changeView('grid')" :class="[
                                    'flex-1 flex items-center justify-center gap-2 py-2 px-3 border',
                                    toggleGrid
                                        ? 'bg-red-600 text-white border-red-600'
                                        : 'bg-white dark:bg-zinc-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-zinc-600'
                                ]">
                                    <Icon icon="ep:grid" class="w-4 h-4" />
                                    Grid
                                </button>
                                <button @click="changeView('list')" :class="[
                                    'flex-1 flex items-center justify-center gap-2 py-2 px-3 border',
                                    !toggleGrid
                                        ? 'bg-red-600 text-white border-red-600'
                                        : 'bg-white dark:bg-zinc-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-zinc-600'
                                ]">
                                    <Icon icon="ph:list-bold" class="w-4 h-4" />
                                    List
                                </button>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div v-if="isLoadingFilters" class="py-8">
                            <div class="flex justify-center">
                                <CoreLoader :width="20" :height="20" color="red" />
                            </div>
                            <p class="text-center mt-2 text-zinc-600 dark:text-zinc-400">Loading filters...</p>
                        </div>

                        <div v-else class="space-y-6">
                            <!-- Services Filter -->
                            <div>
                                <h3 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3">Services</h3>
                                <div class="space-y-2 max-h-40 overflow-y-auto">
                                    <div v-if="filterOptions.services.length === 0" class="text-zinc-500 dark:text-zinc-400 text-sm italic">
                                        No services available
                                    </div>
                                    <div v-for="service in filterOptions.services" :key="service.id" class="flex items-center">
                                        <input
                                            type="checkbox"
                                            :id="`mobile-service-${service.id}`"
                                            :value="service.id"
                                            v-model="selectedServices"
                                            @change="applyFilters"
                                            class="h-4 w-4 rounded border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                        >
                                        <label :for="`mobile-service-${service.id}`" class="ml-2 text-sm text-zinc-600 dark:text-zinc-400">
                                            {{ service.name }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Price Range Filter -->
                            <div>
                                <h3 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3">Price Range</h3>
                                <div class="space-y-3">
                                    <div class="flex justify-between text-sm text-zinc-600 dark:text-zinc-400">
                                        <span>{{ formatCurrency(priceRange[0]) }}</span>
                                        <span>{{ formatCurrency(priceRange[1]) }}</span>
                                    </div>
                                    <input
                                        type="range"
                                        v-model="priceRange[0]"
                                        :min="filterOptions.price_range.min"
                                        :max="filterOptions.price_range.max"
                                        @change="applyFilters"
                                        class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                                    >
                                    <input
                                        type="range"
                                        v-model="priceRange[1]"
                                        :min="filterOptions.price_range.min"
                                        :max="filterOptions.price_range.max"
                                        @change="applyFilters"
                                        class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                                    >
                                </div>
                            </div>

                            <!-- Location Filter -->
                            <div>
                                <h3 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3">Location</h3>
                                <div class="space-y-2 max-h-40 overflow-y-auto">
                                    <div v-if="filterOptions.locations.length === 0" class="text-zinc-500 dark:text-zinc-400 text-sm italic">
                                        No locations available
                                    </div>
                                    <div v-for="(location, index) in filterOptions.locations" :key="index" class="flex items-center">
                                        <input
                                            type="checkbox"
                                            :id="`mobile-location-${index}`"
                                            :value="location"
                                            v-model="selectedLocations"
                                            @change="applyFilters"
                                            class="h-4 w-4 rounded border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                        >
                                        <label :for="`mobile-location-${index}`" class="ml-2 text-sm text-zinc-600 dark:text-zinc-400">
                                            {{ location }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Rating Filter -->
                            <div>
                                <h3 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3">Minimum Rating</h3>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <input
                                            type="radio"
                                            id="mobile-rating-any"
                                            name="mobile-rating"
                                            :value="0"
                                            v-model="minRating"
                                            @change="applyFilters"
                                            class="h-4 w-4 border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                        >
                                        <label for="mobile-rating-any" class="ml-2 text-sm text-zinc-600 dark:text-zinc-400">Any rating</label>
                                    </div>
                                    <div v-for="rating in [3, 4]" :key="rating" class="flex items-center">
                                        <input
                                            type="radio"
                                            :id="`mobile-rating-${rating}`"
                                            name="mobile-rating"
                                            :value="rating"
                                            v-model="minRating"
                                            @change="applyFilters"
                                            class="h-4 w-4 border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                        >
                                        <label :for="`mobile-rating-${rating}`" class="ml-2 text-sm text-zinc-600 dark:text-zinc-400 flex items-center">
                                            {{ rating }}+
                                            <div class="flex ml-1">
                                                <svg v-for="i in rating" :key="i" class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                </svg>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Availability Filter -->
                            <div>
                                <h3 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3">Availability</h3>
                                <div class="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="mobile-available-only"
                                        v-model="availableOnly"
                                        @change="applyFilters"
                                        class="h-4 w-4 rounded border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-red-600 focus:ring-red-500"
                                    >
                                    <label for="mobile-available-only" class="ml-2 text-sm text-zinc-600 dark:text-zinc-400">
                                        Available vendors only
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="border-t dark:border-zinc-700 p-4">
                        <div class="flex gap-3">
                            <button @click="resetFilters" class="flex-1 py-2 px-4 border border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 font-medium">
                                Reset All
                            </button>
                            <button @click="showMobileFilters = false" class="flex-1 py-2 px-4 bg-red-600 text-white font-medium">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 md:mt-10">
            <div
                class="flex items-center flex-col md:flex-row gap-4 md:gap-6 bg-gradient-to-tr from-red-700 to-pink-500 text-white px-4 md:px-6 py-4 md:py-3.5">
                <p class="text-sm md:text-base flex-1 text-center md:text-left">
                    <span class="text-lg md:text-xl font-semibold flex items-center justify-center md:justify-start">
                        <Icon icon="mage:megaphone-a" class="h-5 w-5 md:h-6 md:w-6 mr-2" /> Did you know?
                    </span>
                    You can become a vendor on EventaHub and start earning money by providing your services to our
                    community
                </p>

                <div>
                    <button @click="sendRequest()" type="button" class="bg-white dark:bg-zinc-800 text-black dark:text-white py-2 md:py-2.5 px-4 md:px-5 text-sm md:text-base font-medium border dark:border-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors">
                        Send request
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Listbox, ListboxButton, ListboxOptions, ListboxOption } from '@headlessui/vue'
import { CheckIcon, ChevronDownIcon } from '@heroicons/vue/24/outline';
import { useAuthStore } from '@/store/auth';
import { useUserMappingStore } from '@/store/userMapping';
import type { Pagination, ApiResponse, Vendor, ExpandedState, ApiVendor, FilterOptions } from '@/types/vendor';

import VendorCard from '@/components/vendor/Card.vue';
import VendorCardSkeleton from '@/components/vendor/CardSkeleton.vue';
import VendorListview from '@/components/vendor/Listview.vue';
import VendorListviewSkeleton from '@/components/vendor/ListviewSkeleton.vue';
import VendorHero from '@/components/vendor/Hero.vue';

definePageMeta({
    layout: 'default',
    title: 'Vendors',
    meta: [
        {
            name: 'description',
            content: 'Find the perfect vendors for your event'
        }
    ]
});

useHead({
    title: 'Event Vendors & Services in Malawi - Wedding, Party & Event Suppliers | EventaHub',
    meta: [
        {
            name: 'description',
            content: 'Find and book the perfect vendors for your event in Malawi. Browse photographers, caterers, decorators, DJs, and more event services for weddings, parties, and corporate events.'
        },
        {
            name: 'keywords',
            content: 'event vendors Malawi, wedding vendors, party vendors, event services, photographers, caterers, decorators, DJs, event suppliers'
        },
        {
            property: 'og:title',
            content: 'Event Vendors & Services in Malawi - Wedding, Party & Event Suppliers | EventaHub'
        },
        {
            property: 'og:description',
            content: 'Find and book the perfect vendors for your event in Malawi. Browse photographers, caterers, decorators, DJs, and more event services.'
        },
        {
            property: 'og:type',
            content: 'website'
        },
        {
            name: 'twitter:card',
            content: 'summary_large_image'
        },
        {
            name: 'twitter:title',
            content: 'Event Vendors & Services in Malawi | EventaHub'
        },
        {
            name: 'twitter:description',
            content: 'Find and book the perfect vendors for your event in Malawi. Browse photographers, caterers, and more.'
        },
        {
            name: 'robots',
            content: 'index, follow'
        }
    ]
});

const searchQuery = ref<string>('');
const toggleGrid = ref<boolean>(true);
const sortOption = ref<string>('rating');
const authStore = useAuthStore();
const userMappingStore = useUserMappingStore();
const router = useRouter();
const route = useRoute();
const { $toast }: any = useNuxtApp();
const httpClient = useHttpClient();
const isLoading = ref<boolean>(true);
const isLoadingFilters = ref<boolean>(true);
const pagination = ref<Pagination | null>(null);
const vendors = ref<Vendor[]>([]);

const showMobileFilters = ref<boolean>(false);

const filterOptions = ref<FilterOptions>({
    services: [],
    price_range: { min: 0, max: 10000 },
    locations: [],
    rating_range: { min: 0, max: 5 }
});

const selectedServices = ref<number[]>([]);
const priceRange = ref<number[]>([0, 10000]);
const selectedLocations = ref<string[]>([]);
const minRating = ref<number>(0);
const availableOnly = ref<boolean>(false);

const sortOptions = [
    { value: 'rating', label: 'Rating (High to Low)' },
    { value: 'price-low', label: 'Price (Low to High)' },
    { value: 'price-high', label: 'Price (High to Low)' },
    { value: 'reviews', label: 'Number of Reviews' },
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' }
];

const getSortLabel = (value: string): string => {
    return sortOptions.find(option => option.value === value)?.label || '';
};

const activeFiltersCount = computed(() => {
    let count = 0;
    if (selectedServices.value.length > 0) count++;
    if (priceRange.value[0] > filterOptions.value.price_range.min || priceRange.value[1] < filterOptions.value.price_range.max) count++;
    if (selectedLocations.value.length > 0) count++;
    if (minRating.value > 0) count++;
    if (availableOnly.value) count++;
    if (searchQuery.value.trim()) count++;
    return count;
});

const expanded: ExpandedState = reactive({
    services: true,
    priceRange: true,
    location: true,
    rating: true,
    availability: true
});

function toggleSection(section: keyof ExpandedState): void {
    expanded[section] = !expanded[section];
}

const sendRequest = (): void => {
    authStore.authToken ? router.push('/vendors/request') :
    $toast.info("You need to be logged in to send a request");
};

const changeView = (type: "grid" | "list"): void => {
    toggleGrid.value = type === "grid";
};

const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'MWK',
        minimumFractionDigits: 0
    }).format(value);
};

const transformVendors = (apiVendors: ApiVendor[]): Vendor[] => {
    return apiVendors.map(vendor => {
        const priceString = vendor.prices?.[0]?.price || '0';
        const numericPrice = parseFloat(priceString.replace(/,/g, ''));

        const serviceTypes = vendor.services.map(service => service.service.name);

        const workImages = vendor.media.map(media => ({
            url: media.path,
            alt: media.title
        }));

        return {
            id: vendor.id,
            name: vendor.name,
            slug: vendor.slug,
            avatar: vendor.user.avatar,
            isVerified: vendor.is_verified === 1,
            location: vendor.location,
            rating: vendor.ratings_avg_rating ? parseFloat(vendor.ratings_avg_rating.toString()) : 0,
            reviewCount: vendor.ratings_count || 0,
            serviceTypes: serviceTypes,
            description: vendor.bio,
            workImages: workImages,
            startingPrice: numericPrice,
            priceCurrency: vendor.prices?.[0]?.currency?.name || 'MWK',
        };
    });
};

const fetchFilterOptions = async (): Promise<void> => {
    isLoadingFilters.value = true;
    try {
        const response = await httpClient.get(ENDPOINTS.VENDORS.FILTER_OPTIONS);
        if (response) {
            filterOptions.value = response as FilterOptions;
            priceRange.value = [filterOptions.value.price_range.min, filterOptions.value.price_range.max];
        }
    } catch (error) {
        console.error('Error fetching filter options:', error);
        $toast.error('Failed to load filter options');
    } finally {
        isLoadingFilters.value = false;
    }
};

const applyFilters = async (): Promise<void> => {
    isLoading.value = true;

    const params = new URLSearchParams();
    const userParam = route.query.user as string;
    if (userParam) {
        if (userParam.includes('-')) {
            const actualUserId = userMappingStore.getIdFromUuid(userParam);

            if (actualUserId) {
                params.append('user', actualUserId.toString());
            }
        } else {
            params.append('user', userParam);
        }
    }

    if (selectedServices.value.length > 0) {
        params.append('services', selectedServices.value.join(','));
    }

    if (priceRange.value[0] > filterOptions.value.price_range.min) {
        params.append('min_price', priceRange.value[0].toString());
    }

    if (priceRange.value[1] < filterOptions.value.price_range.max) {
        params.append('max_price', priceRange.value[1].toString());
    }

    if (selectedLocations.value.length > 0) {
        params.append('location', selectedLocations.value.join(','));
    }

    if (minRating.value > 0) {
        params.append('min_rating', minRating.value.toString());
    }

    if (availableOnly.value) {
        params.append('is_available', 'true');
    }

    if (searchQuery.value) {
        params.append('search', searchQuery.value);
    }

    if (sortOption.value) {
        params.append('sort', sortOption.value);
    }

    params.append('per_page', '10');

    try {
        const url = `${ENDPOINTS.VENDORS.GET_ALL}?${params.toString()}`;
        const response = await httpClient.get(url);

        if (response) {
            const apiResponse = response as ApiResponse;
            vendors.value = transformVendors(apiResponse.data);
            pagination.value = {
                current_page: apiResponse.current_page,
                first_page_url: apiResponse.first_page_url,
                from: apiResponse.from,
                last_page: apiResponse.last_page,
                last_page_url: apiResponse.last_page_url,
                links: apiResponse.links,
                next_page_url: apiResponse.next_page_url,
                path: apiResponse.path,
                per_page: apiResponse.per_page,
                prev_page_url: apiResponse.prev_page_url,
                to: apiResponse.to,
                total: apiResponse.total
            };
        }
    } catch (error) {
        handleErrorWithToast(error, $toast);
    } finally {
        isLoading.value = false;
    }
};

const resetFilters = (): void => {
    selectedServices.value = [];
    priceRange.value = [filterOptions.value.price_range.min, filterOptions.value.price_range.max];
    selectedLocations.value = [];
    minRating.value = 0;
    availableOnly.value = false;
    searchQuery.value = '';
    sortOption.value = 'rating';
    showMobileFilters.value = false;

    applyFilters();
};

const getPageNumbers = (): number[] => {
    if (!pagination.value) return [];

    const currentPage = pagination.value.current_page;
    const lastPage = pagination.value.last_page;

    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(lastPage, startPage + 4);

    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }

    const pages: number[] = [];
    for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
    }

    return pages;
};

const changePage = (page: number): void => {
    if (!pagination.value) return;

    if (page < 1 || page > pagination.value.last_page) {
        return;
    }

    const params = new URLSearchParams();

    if (selectedServices.value.length > 0) {
        params.append('services', selectedServices.value.join(','));
    }

    if (priceRange.value[0] > filterOptions.value.price_range.min) {
        params.append('min_price', priceRange.value[0].toString());
    }

    if (priceRange.value[1] < filterOptions.value.price_range.max) {
        params.append('max_price', priceRange.value[1].toString());
    }

    if (selectedLocations.value.length > 0) {
        params.append('location', selectedLocations.value.join(','));
    }

    if (minRating.value > 0) {
        params.append('min_rating', minRating.value.toString());
    }

    if (availableOnly.value) {
        params.append('is_available', 'true');
    }

    if (searchQuery.value) {
        params.append('search', searchQuery.value);
    }

    if (sortOption.value) {
        params.append('sort', sortOption.value);
    }

    params.append('page', page.toString());
    params.append('per_page', '10');

    const url = `${ENDPOINTS.VENDORS.GET_ALL}?${params.toString()}`;
    fetchVendorsByUrl(url);
};

const fetchVendorsByUrl = async (url: string): Promise<void> => {
    isLoading.value = true;
    try {
        const response = await httpClient.get(url);
        if (response) {
            const apiResponse = response as ApiResponse;
            vendors.value = transformVendors(apiResponse.data);
            pagination.value = {
                current_page: apiResponse.current_page,
                first_page_url: apiResponse.first_page_url,
                from: apiResponse.from,
                last_page: apiResponse.last_page,
                last_page_url: apiResponse.last_page_url,
                links: apiResponse.links,
                next_page_url: apiResponse.next_page_url,
                path: apiResponse.path,
                per_page: apiResponse.per_page,
                prev_page_url: apiResponse.prev_page_url,
                to: apiResponse.to,
                total: apiResponse.total
            };
        }
    } catch (error) {
        handleErrorWithToast(error, $toast);
    } finally {
        isLoading.value = false;
    }
};

onMounted(async () => {
    await fetchFilterOptions();
    applyFilters();
});
</script>
